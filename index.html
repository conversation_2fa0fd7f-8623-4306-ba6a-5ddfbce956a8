<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AES加密解密演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            flex: 1;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background-color: #e9f7ef;
            border-left: 4px solid #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AES加密解密演示</h1>
        
        <div class="form-group">
            <label for="input-text">输入文本:</label>
            <textarea id="input-text" rows="4" placeholder="请输入要加密或解密的文本"></textarea>
        </div>
        
        <div class="buttons">
            <button id="encrypt-btn">加密</button>
            <button id="decrypt-btn">解密</button>
        </div>
        
        <div class="result" id="result-container" style="display: none;">
            <h3>结果:</h3>
            <div id="result-text"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script>
        // 与Java程序相同的密钥
        const secretKeyStr = "BUYHg67f@WDC1qfv4raz#ESXm89nPOKL";
        
        // 生成密钥和IV
        function generateKey() {
            // 使用SHA1作为PRNG，模拟Java中的SHA1PRNG
            const hash = CryptoJS.SHA1(secretKeyStr);
            // 取前128位作为AES密钥
            return CryptoJS.enc.Hex.parse(hash.toString().substring(0, 32));
        }
        
        // 加密函数
        function encrypt(plaintext) {
            try {
                const key = generateKey();
                // 生成随机IV (16字节)
                const iv = CryptoJS.lib.WordArray.random(16);
                
                // 使用AES-CBC模式加密
                const encrypted = CryptoJS.AES.encrypt(plaintext, key, { 
                    iv: iv,
                    padding: CryptoJS.pad.Pkcs7,
                    mode: CryptoJS.mode.CBC
                });
                
                // 将IV和加密数据结合
                const ivAndEncrypted = iv.concat(encrypted.ciphertext);
                
                // 返回Base64编码结果
                return CryptoJS.enc.Base64.stringify(ivAndEncrypted);
            } catch (e) {
                console.error("加密失败:", e);
                return "加密错误: " + e.message;
            }
        }
        
        // 解密函数
        function decrypt(encryptedText) {
            try {
                const key = generateKey();
                
                // Base64解码
                const ivAndEncrypted = CryptoJS.enc.Base64.parse(encryptedText);
                
                // 分离IV(前16字节)和加密数据
                const iv = CryptoJS.lib.WordArray.create(ivAndEncrypted.words.slice(0, 4));
                const encrypted = CryptoJS.lib.WordArray.create(
                    ivAndEncrypted.words.slice(4),
                    ivAndEncrypted.sigBytes - 16
                );
                
                // 创建加密对象
                const cipherParams = CryptoJS.lib.CipherParams.create({
                    ciphertext: encrypted
                });
                
                // 使用AES-CBC模式解密
                const decrypted = CryptoJS.AES.decrypt(
                    cipherParams, 
                    key, 
                    { 
                        iv: iv,
                        padding: CryptoJS.pad.Pkcs7,
                        mode: CryptoJS.mode.CBC
                    }
                );
                console.log(decrypted.toString(CryptoJS.enc.Utf8));
                return decrypted.toString(CryptoJS.enc.Utf8);
            } catch (e) {
                console.error("解密失败:", e);
                return "解密错误: " + e.message;
            }
        }
        
        // 事件监听
        document.getElementById('encrypt-btn').addEventListener('click', function() {
            const inputText = document.getElementById('input-text').value;
            if (!inputText) {
                alert('请输入要加密的文本!');
                return;
            }
            
            const result = encrypt(inputText);
            displayResult(result);
        });
        
        document.getElementById('decrypt-btn').addEventListener('click', function() {
            const inputText = document.getElementById('input-text').value;
            if (!inputText) {
                alert('请输入要解密的文本!');
                return;
            }
            
            const result = decrypt(inputText);
            displayResult(result);
        });
        
        function displayResult(result) {
            const resultContainer = document.getElementById('result-container');
            const resultText = document.getElementById('result-text');
            
            resultText.textContent = result;
            resultContainer.style.display = 'block';
        }
    </script>
</body>
</html> 