<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重复任职数据分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .input-section {
            margin-bottom: 30px;
        }
        
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #333;
        }
        
        textarea {
            width: 100%;
            height: 200px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            box-sizing: border-box;
        }
        
        textarea:focus {
            border-color: #4CAF50;
            outline: none;
        }
        
        .button-section {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        .results {
            margin-top: 30px;
        }
        
        .result-section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid;
            position: relative;
        }
        
        .normal-data {
            background-color: #e8f5e8;
            border-left-color: #4CAF50;
        }
        
        .concurrent-data {
            background-color: #fff3cd;
            border-left-color: #ffc107;
        }
        
        .duplicate-data {
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .result-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .copy-button {
            background-color: #6c757d;
            color: white;
            padding: 5px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin: 0;
        }
        
        .copy-button:hover {
            background-color: #5a6268;
        }
        
        .copy-button:active {
            background-color: #28a745;
        }
        
        .copy-success {
            background-color: #28a745 !important;
        }
        
        .code-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .code-item {
            background-color: rgba(255,255,255,0.8);
            padding: 6px 12px;
            border-radius: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            border: 1px solid rgba(0,0,0,0.1);
        }
        
        .count {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #f5c6cb;
            margin-top: 20px;
        }
        
    </style>
</head>
<body>
    <div class="container">
        <h1>重复任职数据分析</h1>
        
        <div class="input-section">
            <label for="jsonInput">请输入JSON数据:</label>
            <textarea id="jsonInput" placeholder="请在此处输入JSON数据..."></textarea>
        </div>
        
        <div class="button-section">
            <button onclick="analyzeData()">分析数据</button>
            <button onclick="clearData()">清空数据</button>
        </div>
        
        <div id="results" class="results" style="display: none;">
            <div class="result-section normal-data">
                <div class="result-title">
                    <span>正常数据（无兼职）</span>
                    <button class="copy-button" onclick="copyToClipboard('normal')">复制</button>
                </div>
                <div id="normalCodes" class="code-list"></div>
                <div id="normalCount" class="count"></div>
            </div>
            
            <div class="result-section concurrent-data">
                <div class="result-title">
                    <span>正常数据（有兼职）</span>
                    <button class="copy-button" onclick="copyToClipboard('concurrent')">复制</button>
                </div>
                <div id="concurrentCodes" class="code-list"></div>
                <div id="concurrentCount" class="count"></div>
            </div>
            
            <div class="result-section duplicate-data">
                <div class="result-title">
                    <span>主任职重复</span>
                    <button class="copy-button" onclick="copyToClipboard('duplicate')">复制</button>
                </div>
                <div id="duplicateCodes" class="code-list"></div>
                <div id="duplicateCount" class="count"></div>
            </div>
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        // 全局变量存储分析结果
        let analysisResults = {
            normal: [],
            concurrent: [],
            duplicate: []
        };

        function analyzeData() {
            const jsonInput = document.getElementById('jsonInput').value.trim();
            const errorDiv = document.getElementById('error');
            const resultsDiv = document.getElementById('results');
            
            // 隐藏之前的错误和结果
            errorDiv.style.display = 'none';
            resultsDiv.style.display = 'none';
            
            if (!jsonInput) {
                showError('请输入JSON数据');
                return;
            }
            
            try {
                const data = JSON.parse(jsonInput);
                
                // 验证数据结构
                if (!data.page || !data.page.records || !Array.isArray(data.page.records)) {
                    showError('JSON数据格式不正确，缺少 page.records 字段或 records 不是数组');
                    return;
                }
                
                const records = data.page.records;
                
                // 按code分组
                const codeGroups = {};
                records.forEach(record => {
                    if (!record.code || !record.codeType) {
                        return; // 跳过缺少必要字段的记录
                    }
                    
                    if (!codeGroups[record.code]) {
                        codeGroups[record.code] = [];
                    }
                    codeGroups[record.code].push(record);
                });
                
                // 分析数据
                const normalCodes = []; // 正常数据（无兼职）
                const concurrentCodes = []; // 正常数据（有兼职）
                const duplicateCodes = []; // 主任职重复
                
                Object.keys(codeGroups).forEach(code => {
                    const group = codeGroups[code];
                    const codeTypes = group.map(item => item.codeType);
                    const type1Count = codeTypes.filter(type => type === "1").length;
                    const type2Count = codeTypes.filter(type => type === "2").length;
                    
                    if (group.length === 1) {
                        // 只有一条记录，无兼职
                        normalCodes.push(code);
                    } else if (type1Count === 1 && type2Count >= 1) {
                        // 有一个主任职(codeType=1)和多个兼职(codeType=2)
                        concurrentCodes.push(code);
                    } else if (type1Count > 1) {
                        // 有多个主任职(codeType=1)，属于重复
                        duplicateCodes.push(code);
                    }
                    // 其他情况不处理（如只有codeType=2的记录等）
                });
                
                // 保存结果到全局变量
                analysisResults = {
                    normal: normalCodes,
                    concurrent: concurrentCodes,
                    duplicate: duplicateCodes
                };
                
                // 显示结果
                displayResults(normalCodes, concurrentCodes, duplicateCodes);
                
            } catch (e) {
                showError('JSON数据格式错误: ' + e.message);
            }
        }
        
        function displayResults(normalCodes, concurrentCodes, duplicateCodes) {
            // 显示正常数据（无兼职）
            const normalCodesDiv = document.getElementById('normalCodes');
            const normalCountDiv = document.getElementById('normalCount');
            normalCodesDiv.innerHTML = normalCodes.map(code => 
                `<span class="code-item">${code}</span>`
            ).join('');
            normalCountDiv.textContent = `共 ${normalCodes.length} 个`;
            
            // 显示正常数据（有兼职）
            const concurrentCodesDiv = document.getElementById('concurrentCodes');
            const concurrentCountDiv = document.getElementById('concurrentCount');
            concurrentCodesDiv.innerHTML = concurrentCodes.map(code => 
                `<span class="code-item">${code}</span>`
            ).join('');
            concurrentCountDiv.textContent = `共 ${concurrentCodes.length} 个`;
            
            // 显示主任职重复
            const duplicateCodesDiv = document.getElementById('duplicateCodes');
            const duplicateCountDiv = document.getElementById('duplicateCount');
            duplicateCodesDiv.innerHTML = duplicateCodes.map(code => 
                `<span class="code-item">${code}</span>`
            ).join('');
            duplicateCountDiv.textContent = `共 ${duplicateCodes.length} 个`;
            
            // 显示结果区域
            document.getElementById('results').style.display = 'block';
        }
        
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        function clearData() {
            document.getElementById('jsonInput').value = '';
            document.getElementById('results').style.display = 'none';
            document.getElementById('error').style.display = 'none';
        }
        

        function copyToClipboard(type) {
            const codes = analysisResults[type];
            if (codes.length === 0) {
                alert('该类别暂无数据');
                return;
            }
            
            // 用英文逗号分隔
            const textToCopy = codes.join(',');
            
            // 使用现代的 Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(textToCopy).then(() => {
                    showCopySuccess(type);
                }).catch(err => {
                    // 如果失败，使用备用方法
                    fallbackCopyToClipboard(textToCopy, type);
                });
            } else {
                // 使用备用方法
                fallbackCopyToClipboard(textToCopy, type);
            }
        }
        
        function fallbackCopyToClipboard(text, type) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                showCopySuccess(type);
            } catch (err) {
                alert('复制失败，请手动选择并复制: ' + text);
            } finally {
                document.body.removeChild(textArea);
            }
        }
        
        function showCopySuccess(type) {
            const button = document.querySelector(`button[onclick="copyToClipboard('${type}')"]`);
            const originalText = button.textContent;
            const originalClass = button.className;
            
            button.textContent = '已复制';
            button.className = originalClass + ' copy-success';
            
            setTimeout(() => {
                button.textContent = originalText;
                button.className = originalClass;
            }, 1500);
        }
    </script>
</body>
</html> 