import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Scanner;

public class AESCryptoDemo {
    private static final String secretKeyStr = "BUYHg67f@WDC1qfv4raz#ESXm89nPOKL";
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            System.out.println("\nSelect operation:");
            System.out.println("1. Encrypt");
            System.out.println("2. Decrypt");
            System.out.println("3. Exit");
            System.out.print("Enter choice (1-3): ");
            
            int choice;
            try {
                choice = scanner.nextInt();
                scanner.nextLine(); // Clear input buffer
            } catch (Exception e) {
                System.out.println("Invalid input, please try again.");
                scanner.nextLine(); // Clear input buffer
                continue;
            }
            
            if (choice == 3) {
                System.out.println("Program exited.");
                break;
            }
            
            try {
                switch (choice) {
                    case 1:
                        System.out.print("Enter text to encrypt: ");
                        String plaintext = scanner.nextLine();
                        String encrypted = encrypt(plaintext);
                        System.out.println("Encryption result: " + encrypted);
                        break;
                    case 2:
                        System.out.print("Enter text to decrypt: ");
                        String encryptedText = scanner.nextLine();
                        String decrypted = decrypt(encryptedText);
                        System.out.println("Decryption result: " + decrypted);
                        break;
                    default:
                        System.out.println("Invalid option, please try again.");
                        break;
                }
            } catch (Exception e) {
                System.out.println("Operation failed: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        scanner.close();
    }

    private static SecretKey generateKey() throws Exception {
        // Use SHA1PRNG to match the JavaScript implementation
        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(secretKeyStr.getBytes(StandardCharsets.UTF_8));
        keyGen.init(128, secureRandom);
        return keyGen.generateKey();
    }

    private static String encrypt(String plaintext) throws Exception {
        // Generate key from seed
        SecretKey secretKey = generateKey();
        
        // Generate random IV (16 bytes)
        byte[] iv = new byte[16];
        SecureRandom random = new SecureRandom();
        random.nextBytes(iv);
        
        // Initialize cipher for encryption
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(iv));
        
        // Encrypt the plaintext
        byte[] encrypted = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
        
        // Combine IV and encrypted data
        byte[] ivAndEncrypted = new byte[iv.length + encrypted.length];
        System.arraycopy(iv, 0, ivAndEncrypted, 0, iv.length);
        System.arraycopy(encrypted, 0, ivAndEncrypted, iv.length, encrypted.length);
        
        // Return Base64 encoded result
        return Base64.getEncoder().encodeToString(ivAndEncrypted);
    }

    private static String decrypt(String encryptedText) throws Exception {
        // Generate key from seed
        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(secretKeyStr.getBytes(StandardCharsets.UTF_8));
        keyGen.init(128, secureRandom);
        SecretKey secretKey = keyGen.generateKey();
        
        // Base64 decode
        byte[] ivAndEncrypted = Base64.getDecoder().decode(encryptedText);

        // Separate IV (first 16 bytes) and encrypted data
        byte[] iv = new byte[16];
        byte[] encrypted = new byte[ivAndEncrypted.length - 16];
        System.arraycopy(ivAndEncrypted, 0, iv, 0, 16);
        System.arraycopy(ivAndEncrypted, 16, encrypted, 0, encrypted.length);

        // Initialize cipher and decrypt
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, secretKey, new IvParameterSpec(iv));
        byte[] decrypted = cipher.doFinal(encrypted);

        return new String(decrypted, StandardCharsets.UTF_8);
    }
} 