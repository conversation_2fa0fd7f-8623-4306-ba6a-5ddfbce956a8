/*
 Navicat Premium Dump SQL

 Source Server         : 主数据系统
 Source Server Type    : PostgreSQL
 Source Server Version : 140011 (140011)
 Source Host           : *************:5432
 Source Catalog        : elsbiz
 Source Schema         : sl_baosteeluat

 Target Server Type    : PostgreSQL
 Target Server Version : 140011 (140011)
 File Encoding         : 65001

 Date: 29/07/2025 18:26:55
*/


-- ----------------------------
-- Table structure for UserGroup
-- ----------------------------
DROP TABLE IF EXISTS "sl_baosteeluat"."UserGroup";
CREATE TABLE "sl_baosteeluat"."UserGroup" (
  "id" int8 NOT NULL DEFAULT 0,
  "pagecode" int8 NOT NULL DEFAULT 0,
  "creatorId" int8 NOT NULL DEFAULT 0,
  "createTime" timestamp(6) NOT NULL DEFAULT timezone('utc'::text, now()),
  "lastModifyUserId" int8 NOT NULL DEFAULT 0,
  "lastModifyTime" timestamp(6) NOT NULL DEFAULT timezone('utc'::text, now()),
  "creatorRepresentative" text COLLATE "pg_catalog"."default",
  "lastModifyRepresentative" text COLLATE "pg_catalog"."default",
  "userId" int8 NOT NULL DEFAULT 0,
  "groupId" int8 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "sl_baosteeluat"."UserGroup"."id" IS 'id';
COMMENT ON COLUMN "sl_baosteeluat"."UserGroup"."pagecode" IS 'pagecode';
COMMENT ON COLUMN "sl_baosteeluat"."UserGroup"."creatorId" IS 'creatorId';
COMMENT ON COLUMN "sl_baosteeluat"."UserGroup"."createTime" IS 'createTime';
COMMENT ON COLUMN "sl_baosteeluat"."UserGroup"."lastModifyUserId" IS 'lastModifyUserId';
COMMENT ON COLUMN "sl_baosteeluat"."UserGroup"."lastModifyTime" IS 'lastModifyTime';
COMMENT ON COLUMN "sl_baosteeluat"."UserGroup"."creatorRepresentative" IS 'creatorRepresentative';
COMMENT ON COLUMN "sl_baosteeluat"."UserGroup"."lastModifyRepresentative" IS 'lastModifyRepresentative';
COMMENT ON COLUMN "sl_baosteeluat"."UserGroup"."userId" IS '用户ID';
COMMENT ON COLUMN "sl_baosteeluat"."UserGroup"."groupId" IS '组ID';

-- ----------------------------
-- Indexes structure for table UserGroup
-- ----------------------------
CREATE INDEX "UserGroup_groupId_idx" ON "sl_baosteeluat"."UserGroup" USING btree (
  "groupId" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "UserGroup_userId_idx" ON "sl_baosteeluat"."UserGroup" USING btree (
  "userId" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table UserGroup
-- ----------------------------
ALTER TABLE "sl_baosteeluat"."UserGroup" ADD CONSTRAINT "UserGroup_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table UserGroup
-- ----------------------------
ALTER TABLE "sl_baosteeluat"."UserGroup" ADD CONSTRAINT "fkUserGroupGroupId" FOREIGN KEY ("groupId") REFERENCES "sl_baosteeluat"."Group" ("id") ON DELETE CASCADE ON UPDATE CASCADE DEFERRABLE;
ALTER TABLE "sl_baosteeluat"."UserGroup" ADD CONSTRAINT "fkUserGroupUserId" FOREIGN KEY ("userId") REFERENCES "sl_baosteeluat"."User" ("id") ON DELETE CASCADE ON UPDATE CASCADE DEFERRABLE;
