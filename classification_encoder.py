#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类编码程序
根据四级分类结构生成8位编码
编码规则：
- 一级分类：01000000, 02000000
- 二级分类：01010000, 01020000  
- 三级分类：01010100, 01010200
- 四级分类：01010101, 01010102
"""

import pandas as pd
import numpy as np

def process_classification_data(input_file, output_file):
    """
    处理分类数据并生成编码
    """
    # 读取Excel文件
    df = pd.read_excel(input_file)
    
    # 前向填充合并单元格的空值
    df = df.ffill()
    
    # 创建结果列表
    results = []

    # 用于跟踪各级分类的计数器
    level1_counter = {}
    level2_counter = {}
    level3_counter = {}
    level4_counter = {}

    # 用于存储各级分类的编码映射
    level1_codes = {}
    level2_codes = {}
    level3_codes = {}
    level4_codes = {}

    # 用于跟踪排序顺序的计数器
    level1_sort_counter = {}
    level2_sort_counter = {}
    level3_sort_counter = {}
    level4_sort_counter = {}
    
    # 处理每一行数据
    for _, row in df.iterrows():
        level1 = row['文档一级分类名称']
        level2 = row['文档二级分类名称'] 
        level3 = row['文档三级分类名称']
        level4 = row['文档四级分类名称']
        
        # 处理一级分类
        if level1 not in level1_codes:
            if level1 not in level1_counter:
                level1_counter[level1] = len(level1_counter) + 1
            code = f"{level1_counter[level1]:02d}000000"
            level1_codes[level1] = code

            # 计算排序顺序
            if str(level1) == "0":
                sort_order = ""  # 名称为"0"的不设置排序
            else:
                level1_sort_counter[level1] = (len(level1_sort_counter) + 1) * 10
                sort_order = level1_sort_counter[level1]

            results.append({
                '名称': level1,
                '编码': code,
                '上级编码': '',
                '排序顺序': sort_order,
                '级别名': '一级'
            })
        
        # 处理二级分类
        level2_key = f"{level1}|{level2}"
        if level2_key not in level2_codes:
            if level2_key not in level2_counter:
                level2_counter[level2_key] = len([k for k in level2_counter.keys() if k.startswith(f"{level1}|")]) + 1
            parent_code = level1_codes[level1]
            code = f"{parent_code[:2]}{level2_counter[level2_key]:02d}0000"
            level2_codes[level2_key] = code

            # 计算排序顺序（在同一个一级分类下）
            if str(level2) == "0":
                sort_order = ""  # 名称为"0"的不设置排序
            else:
                level1_sort_key = level1
                if level1_sort_key not in level2_sort_counter:
                    level2_sort_counter[level1_sort_key] = {}
                # 只计算非"0"的项目数量
                non_zero_count = len([k for k in level2_sort_counter[level1_sort_key].keys() if str(k) != "0"])
                level2_sort_counter[level1_sort_key][level2] = (non_zero_count + 1) * 10
                sort_order = level2_sort_counter[level1_sort_key][level2]

            results.append({
                '名称': level2,
                '编码': code,
                '上级编码': parent_code,
                '排序顺序': sort_order,
                '级别名': '二级'
            })
        
        # 处理三级分类
        level3_key = f"{level1}|{level2}|{level3}"
        if level3_key not in level3_codes:
            if level3_key not in level3_counter:
                level3_counter[level3_key] = len([k for k in level3_counter.keys() if k.startswith(f"{level1}|{level2}|")]) + 1
            parent_code = level2_codes[level2_key]
            code = f"{parent_code[:4]}{level3_counter[level3_key]:02d}00"
            level3_codes[level3_key] = code

            # 计算排序顺序（在同一个二级分类下）
            if str(level3) == "0":
                sort_order = ""  # 名称为"0"的不设置排序
            else:
                level2_sort_key = f"{level1}|{level2}"
                if level2_sort_key not in level3_sort_counter:
                    level3_sort_counter[level2_sort_key] = {}
                # 只计算非"0"的项目数量
                non_zero_count = len([k for k in level3_sort_counter[level2_sort_key].keys() if str(k) != "0"])
                level3_sort_counter[level2_sort_key][level3] = (non_zero_count + 1) * 10
                sort_order = level3_sort_counter[level2_sort_key][level3]

            results.append({
                '名称': level3,
                '编码': code,
                '上级编码': parent_code,
                '排序顺序': sort_order,
                '级别名': '三级'
            })
        
        # 处理四级分类（如果存在且不为空）
        if pd.notna(level4) and str(level4).strip():
            level4_key = f"{level1}|{level2}|{level3}|{level4}"
            if level4_key not in level4_codes:
                if level4_key not in level4_counter:
                    level4_counter[level4_key] = len([k for k in level4_counter.keys() if k.startswith(f"{level1}|{level2}|{level3}|")]) + 1
                parent_code = level3_codes[level3_key]
                code = f"{parent_code[:6]}{level4_counter[level4_key]:02d}"
                level4_codes[level4_key] = code

                # 计算排序顺序（在同一个三级分类下）
                if str(level4) == "0":
                    sort_order = ""  # 名称为"0"的不设置排序
                else:
                    level3_sort_key = f"{level1}|{level2}|{level3}"
                    if level3_sort_key not in level4_sort_counter:
                        level4_sort_counter[level3_sort_key] = {}
                    # 只计算非"0"的项目数量
                    non_zero_count = len([k for k in level4_sort_counter[level3_sort_key].keys() if str(k) != "0"])
                    level4_sort_counter[level3_sort_key][level4] = (non_zero_count + 1) * 10
                    sort_order = level4_sort_counter[level3_sort_key][level4]

                results.append({
                    '名称': level4,
                    '编码': code,
                    '上级编码': parent_code,
                    '排序顺序': sort_order,
                    '级别名': '四级'
                })
    
    # 去重（保持顺序）
    seen = set()
    unique_results = []
    for item in results:
        key = (item['名称'], item['编码'])
        if key not in seen:
            seen.add(key)
            unique_results.append(item)
    
    # 创建DataFrame
    result_df = pd.DataFrame(unique_results)

    # 确保编码列为字符串格式，并补齐8位
    result_df['编码'] = result_df['编码'].astype(str).str.zfill(8)

    # 处理上级编码列
    result_df['上级编码'] = result_df['上级编码'].apply(lambda x: x.zfill(8) if x else '')

    # 保存到Excel，确保格式正确
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        result_df.to_excel(writer, index=False, sheet_name='分类编码')

        # 设置列宽和格式
        worksheet = writer.sheets['分类编码']
        worksheet.column_dimensions['A'].width = 30  # 名称列
        worksheet.column_dimensions['B'].width = 12  # 编码列
        worksheet.column_dimensions['C'].width = 12  # 上级编码列
        worksheet.column_dimensions['D'].width = 12  # 排序顺序列
        worksheet.column_dimensions['E'].width = 10  # 级别名列

        # 设置格式
        for row in range(2, len(result_df) + 2):  # 从第2行开始（跳过标题）
            worksheet[f'B{row}'].number_format = "@"  # 编码列设为文本格式
            worksheet[f'C{row}'].number_format = "@"  # 上级编码列设为文本格式
    
    print(f"处理完成！")
    print(f"输入文件：{input_file}")
    print(f"输出文件：{output_file}")
    print(f"共生成 {len(unique_results)} 条分类记录")
    
    return result_df

if __name__ == "__main__":
    input_file = "新分类.xlsx"
    output_file = "分类编码结果_完整版.xlsx"
    
    try:
        result_df = process_classification_data(input_file, output_file)
        print("\n前10条记录预览：")
        print(result_df.head(10).to_string(index=False))
    except Exception as e:
        print(f"处理过程中出现错误：{e}")
        import traceback
        traceback.print_exc()
