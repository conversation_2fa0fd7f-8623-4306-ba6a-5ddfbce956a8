#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看生成的Excel文件内容
"""

import pandas as pd

def view_excel_content(file_path):
    """查看Excel文件内容"""
    try:
        df = pd.read_excel(file_path)
        print(f"Excel文件: {file_path}")
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print("\n列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"{i}. {col}")
        
        print("\n数据内容:")
        print(df.to_string(index=False))
        
    except Exception as e:
        print(f"读取Excel文件时出错：{e}")

if __name__ == "__main__":
    view_excel_content("重复code记录.xlsx")
