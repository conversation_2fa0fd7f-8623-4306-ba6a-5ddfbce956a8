import pandas as pd

def convert_code_to_6_digits(code):
    """
    将编码转换为6位，规则如下：
    - 编码为01的改为010000
    - 编码为0101的改为010100  
    - 编码为10101的改为010101
    """
    if pd.isna(code) or code == "":
        return ""
    
    code_str = str(code).strip()
    
    # 去掉小数点（如果是浮点数）
    if '.' in code_str:
        code_str = code_str.split('.')[0]
    
    # 根据规则转换
    if code_str == "01":
        return "010000"
    elif code_str == "0101":
        return "010100"
    elif code_str == "10101":
        return "010101"
    else:
        # 对于其他编码，补齐到6位
        if len(code_str) <= 6:
            return code_str.ljust(6, '0')
        else:
            return code_str[:6]

def find_parent_code(current_code, all_codes):
    """
    根据编码层级关系找到父节点编号
    """
    if not current_code or len(current_code) <= 2:
        return ""
    
    # 根据编码长度确定层级关系
    # 6位编码：前2位、前4位可能是父节点
    possible_parents = []
    
    if len(current_code) >= 4:
        # 前4位 + "00"
        parent_4 = current_code[:4] + "00"
        if parent_4 in all_codes and parent_4 != current_code:
            possible_parents.append(parent_4)
    
    if len(current_code) >= 2:
        # 前2位 + "0000"
        parent_2 = current_code[:2] + "0000"
        if parent_2 in all_codes and parent_2 != current_code:
            possible_parents.append(parent_2)
    
    # 返回最直接的父节点（最长的匹配）
    if possible_parents:
        return max(possible_parents, key=len)
    
    return ""

def process_table(file_path):
    """
    处理表格数据，输出编号、名称、父节点编号
    """
    # 读取Excel文件，跳过第一行标题
    df = pd.read_excel(file_path, header=1)
    
    results = []
    all_codes = set()
    
    # 遍历每一行数据
    for index, row in df.iterrows():
        # 根据表格结构，提取编码和名称对
        # 表格结构：文档分类代码, 文档分类名称, 文档分类代码, 文档分类名称, 维护部门, 文档分类代码, 文档分类名称
        
        # 处理第1列和第2列（一级分类）
        if not pd.isna(row.iloc[0]) and str(row.iloc[0]).strip():
            code = str(row.iloc[0]).strip()
            name = str(row.iloc[1]).strip() if not pd.isna(row.iloc[1]) else ""
            if name:
                converted_code = convert_code_to_6_digits(code)
                if converted_code:
                    results.append({
                        'code': converted_code,
                        'name': name,
                        'level': 1
                    })
                    all_codes.add(converted_code)
        
        # 处理第3列和第4列（二级分类）
        if not pd.isna(row.iloc[2]) and str(row.iloc[2]).strip():
            code = str(row.iloc[2]).strip()
            name = str(row.iloc[3]).strip() if not pd.isna(row.iloc[3]) else ""
            if name:
                converted_code = convert_code_to_6_digits(code)
                if converted_code:
                    results.append({
                        'code': converted_code,
                        'name': name,
                        'level': 2
                    })
                    all_codes.add(converted_code)
        
        # 处理第6列和第7列（三级分类）
        if len(row) > 5 and not pd.isna(row.iloc[5]) and str(row.iloc[5]).strip():
            code = str(row.iloc[5]).strip()
            name = str(row.iloc[6]).strip() if len(row) > 6 and not pd.isna(row.iloc[6]) else ""
            if name:
                converted_code = convert_code_to_6_digits(code)
                if converted_code:
                    results.append({
                        'code': converted_code,
                        'name': name,
                        'level': 3
                    })
                    all_codes.add(converted_code)
    
    # 为每个编码找到父节点
    final_results = []
    for item in results:
        parent_code = find_parent_code(item['code'], all_codes)
        final_results.append({
            '编号': item['code'],
            '名称': item['name'],
            '父节点编号': parent_code
        })
    
    # 去重（基于编号）
    seen_codes = set()
    unique_results = []
    for item in final_results:
        if item['编号'] not in seen_codes:
            unique_results.append(item)
            seen_codes.add(item['编号'])
    
    return pd.DataFrame(unique_results)

def main():
    """
    主函数
    """
    input_file = "附件1：非结构文档分类标准.xlsx"
    
    try:
        # 处理表格
        result_df = process_table(input_file)
        
        # 输出到CSV文件
        output_file = "编码转换结果.csv"
        result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        # 输出到控制台
        print("编码转换结果：")
        print("=" * 80)
        print(f"{'编号':<10} {'名称':<30} {'父节点编号':<10}")
        print("-" * 80)
        
        for index, row in result_df.iterrows():
            print(f"{row['编号']:<10} {row['名称']:<30} {row['父节点编号']:<10}")
        
        print(f"\n结果已保存到: {output_file}")
        print(f"共处理 {len(result_df)} 条记录")
        
        # 输出统计信息
        print(f"\n统计信息：")
        one_level = len(result_df[result_df['编号'].str.endswith('0000')])
        two_level = len(result_df[result_df['编号'].str.endswith('00') & ~result_df['编号'].str.endswith('0000')])
        three_level = len(result_df[~result_df['编号'].str.endswith('00')])
        
        print(f"一级分类（6位编码以0000结尾）: {one_level} 个")
        print(f"二级分类（6位编码以00结尾但不以0000结尾）: {two_level} 个")
        print(f"三级分类（其他）: {three_level} 个")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
