import pandas as pd
import numpy as np

def generate_document_codes(file_path):
    # 读取Excel文件
    df = pd.read_excel(file_path, sheet_name='文档分类', header=1)
    
    # 基础代码
    base_code = "20000063"
    
    # 初始化变量
    current_level1_code = ""
    current_level1_name = ""
    current_level2_code = ""
    current_level2_name = ""
    
    results = []
    
    # 遍历每一行
    for index, row in df.iterrows():
        # 获取各列值，处理NaN
        colA = str(row.iloc[0]) if not pd.isna(row.iloc[0]) else ""
        colB = str(row.iloc[1]) if not pd.isna(row.iloc[1]) else ""
        colC = str(row.iloc[2]) if not pd.isna(row.iloc[2]) else ""
        colD = str(row.iloc[3]) if not pd.isna(row.iloc[3]) else ""
        colF = str(row.iloc[5]) if not pd.isna(row.iloc[5]) else ""
        colG = str(row.iloc[6]) if not pd.isna(row.iloc[6]) else ""
        
        # 更新一级分类
        if colA:
            current_level1_code = colA.zfill(2)
            current_level1_name = colB
            # 重置二级分类
            current_level2_code = ""
            current_level2_name = ""
        
        # 更新二级分类
        if colC:
            # 取后两位作为二级代码
            current_level2_code = colC[-2:].zfill(2)
            current_level2_name = colD
        
        # 生成一级分类编码
        if colA and not colC and not colF:
            code = f"{base_code}{current_level1_code}0000"
            results.append({
                "分类代码": code,
                "分类名称": current_level1_name,
                "级别": "一级分类"
            })
        
        # 生成二级分类编码
        if colC and not colF:
            # 确保有一级分类
            if current_level1_code:
                code = f"{base_code}{current_level1_code}{current_level2_code}00"
                results.append({
                    "分类代码": code,
                    "分类名称": current_level2_name,
                    "级别": "二级分类"
                })
        
        # 生成三级分类编码
        if colF:
            # 取后两位作为三级代码
            level3_code = colF[-2:].zfill(2)
            # 确保有一级和二级分类
            if current_level1_code and current_level2_code:
                code = f"{base_code}{current_level1_code}{current_level2_code}{level3_code}"
                results.append({
                    "分类代码": code,
                    "分类名称": colG,
                    "级别": "三级分类"
                })
    
    return pd.DataFrame(results)

if __name__ == "__main__":
    # 输入文件路径
    input_file = "附件1：非结构文档分类标准.xlsx"
    
    # 生成编码
    result_df = generate_document_codes(input_file)
    
    # 输出文件路径
    output_file = "文档分类编码结果.xlsx"
    
    # 保存结果到Excel
    result_df.to_excel(output_file, index=False)
    
    print(f"编码生成完成，结果已保存到: {output_file}")
    print(f"共生成 {len(result_df)} 个分类编码")