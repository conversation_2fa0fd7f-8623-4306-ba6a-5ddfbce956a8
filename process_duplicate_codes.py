#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理JSON数据中重复code的记录，并输出到Excel文件
"""

import json
import pandas as pd
from collections import defaultdict
import os

def load_json_data(file_path):
    """加载JSON数据文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"错误：JSON格式错误 - {e}")
        return None

def find_duplicate_codes(records):
    """找出重复的code及其对应的记录"""
    code_groups = defaultdict(list)
    
    # 按code分组
    for record in records:
        code = record.get('code')
        if code:
            code_groups[code].append(record)
    
    # 找出重复的code（出现次数大于1）
    duplicate_codes = {code: records for code, records in code_groups.items() if len(records) > 1}
    
    return duplicate_codes

def create_excel_output(duplicate_codes, output_file):
    """创建Excel文件输出重复code的记录"""
    all_duplicate_records = []
    
    # 收集所有重复记录
    for code, records in duplicate_codes.items():
        for record in records:
            # 添加一个字段标识这是重复的code
            record_copy = record.copy()
            record_copy['duplicate_count'] = len(records)
            all_duplicate_records.append(record_copy)
    
    if not all_duplicate_records:
        print("没有找到重复的code记录")
        return
    
    # 创建DataFrame
    df = pd.DataFrame(all_duplicate_records)
    
    # 重新排列列的顺序，把重要信息放在前面
    columns_order = ['code', 'name', 'duplicate_count', 'organization', 'account', 
                    'jobCode', 'codeType', 'orgCode', 'state']
    
    # 确保所有列都存在
    existing_columns = [col for col in columns_order if col in df.columns]
    other_columns = [col for col in df.columns if col not in columns_order]
    final_columns = existing_columns + other_columns
    
    df = df[final_columns]
    
    # 按code排序，这样相同code的记录会相邻显示
    df = df.sort_values(['code', 'name'])
    
    # 保存到Excel
    try:
        df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"成功保存到 {output_file}")
        print(f"共找到 {len(duplicate_codes)} 个重复的code")
        print(f"涉及 {len(all_duplicate_records)} 条记录")
        
        # 打印重复code的统计信息
        print("\n重复code统计：")
        for code, records in duplicate_codes.items():
            names = [record.get('name', 'N/A') for record in records]
            print(f"Code: {code} - 重复次数: {len(records)} - 姓名: {', '.join(names)}")
            
    except Exception as e:
        print(f"保存Excel文件时出错：{e}")

def main():
    """主函数"""
    input_file = "数据.txt"
    output_file = "重复code记录.xlsx"
    
    print("开始处理数据...")
    
    # 加载数据
    data = load_json_data(input_file)
    if not data:
        return
    
    # 获取records数组
    records = data.get('page', {}).get('records', [])
    if not records:
        print("错误：没有找到records数据")
        return
    
    print(f"总共加载了 {len(records)} 条记录")
    
    # 找出重复的code
    duplicate_codes = find_duplicate_codes(records)
    
    if not duplicate_codes:
        print("没有找到重复的code")
        return
    
    # 创建Excel输出
    create_excel_output(duplicate_codes, output_file)

if __name__ == "__main__":
    main()
