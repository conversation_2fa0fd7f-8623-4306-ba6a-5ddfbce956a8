<template>
  <div class="login-redirect-container">
    <div class="content-wrapper">
      <div class="loading-animation">
        <div class="spinner"></div>
        <div class="loading-dots">
          <span class="dot dot1"></span>
          <span class="dot dot2"></span>
          <span class="dot dot3"></span>
        </div>
      </div>
      <h2 class="loading-title">登录跳转中</h2>
      <p class="loading-subtitle">正在为您准备系统环境，请稍候...</p>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { request, useRoute, useRouter } from "utils"

const route = useRoute();
const router = useRouter();
//console.log("router:",router);
//console.log("route:",route);
console.log("code:",route.query.code);
onMounted(async () => {
  const { token } = await request({
    url: "/openapi/api/v2/webHook?companyCode=baosteeluat&coderOrId=getAuthToken",
    method: "POST",
    data: {"usercode":route.query.code}
   
  })
router.push(`/dynamicRoute/DocumentManagement_DMS/0/605649904596942848/605650467837444096?token=${token}`)
})

</script>

<style scoped>
.login-redirect-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4a90e2 0%, #2c5aa0 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  overflow: hidden;
}

.login-redirect-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(135, 206, 250, 0.1) 0%, transparent 50%);
  opacity: 0.6;
}

.content-wrapper {
  text-align: center;
  background: rgba(255, 255, 255, 0.98);
  padding: 3rem 2.5rem;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(74, 144, 226, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(74, 144, 226, 0.2);
  position: relative;
  z-index: 1;
  max-width: 400px;
  width: 90%;
}

.loading-animation {
  margin-bottom: 2rem;
  position: relative;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #e8f4fd;
  border-top: 4px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #4a90e2;
  animation: bounce 1.4s ease-in-out infinite both;
}

.dot1 { animation-delay: -0.32s; }
.dot2 { animation-delay: -0.16s; }
.dot3 { animation-delay: 0s; }

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-title {
  color: #2c5aa0;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  letter-spacing: 0.5px;
}

.loading-subtitle {
  color: #4a90e2;
  font-size: 1rem;
  margin: 0;
  opacity: 0.8;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .content-wrapper {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }

  .loading-title {
    font-size: 1.5rem;
  }

  .loading-subtitle {
    font-size: 0.9rem;
  }

  .spinner {
    width: 50px;
    height: 50px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .login-redirect-container {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  }

  .content-wrapper {
    background: rgba(248, 250, 252, 0.95);
    border: 1px solid rgba(74, 144, 226, 0.3);
  }

  .loading-title {
    color: #1e40af;
  }

  .loading-subtitle {
    color: #3b82f6;
  }

  .spinner {
    border-color: #e0f2fe;
    border-top-color: #3b82f6;
  }

  .dot {
    background-color: #3b82f6;
  }
}
</style>