attachments = getListJSONObjects(newObj, "files");
if ((attachments == null || "{}".equals(attachments.trim())) && 
newObj.getLong("nodeType") != null && newObj.getLong("nodeType").equals(0L)) {
    /** 当是文件夹类型且操作为新增时，判断文件源剩余额度是否足够创建此文件夹 **/
    FormLogic.getJsonFieldsValues(newObj, Arrays.asList("fileSource_baosteeluat", "folderQuato_baosteeluat"), "FileNode", user);
    check = false;
    if (event.equals("add")) {
        check = true;
    }

    if (check) {
        /** 获取新文件夹的文件源ID和分配额度 **/
        Long fileSourceId = newObj.getLong("fileSource_baosteeluat");
        Long folderQuato = newObj.getLong("folderQuato_baosteeluat");
        if (!NumberParser.isNullOrZero(fileSourceId) && !NumberParser.isNullOrZero(folderQuato)) {
            /** 查询文件源的总额度和已分配额度 **/
            sql = getReadSqlExpression("FileSourceSize_baosteeluat", user);
            sql.comparsionAnd("id", ComparsionOperationEnum.EQUAL, fileSourceId); 
            result = getAllObjects("FileSourceSize_baosteeluat", sql, user);
        
            if (!result.isSuccessful()) {
                return result;
            }
            
            item = result.getContent();
            /**return getError("2####"+item);**/
            if (item != null) {
                Long totalQuota = item.getLong("total_quota_size");
                Long allocatedQuota = item.getLong("quato");
                Long remainingQuota = totalQuota - allocatedQuota;
                /**return getError("1111111111111111&&&"+totalQuota+"22222222222####"+allocatedQuota);**/
                /** 检查剩余额度是否足够 **/
                if (folderQuato > remainingQuota) {
                    return getError("文件源空间不足：" + "文件源名称:" + item.getString("title") + 
                                   "，剩余可分配空间: " + remainingQuota + "，申请空间: " + folderQuato);
                }
            }
        }
    }
}

return true;