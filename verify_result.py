import pandas as pd

def verify_sorting_result(file_path):
    """
    验证排序字段是否正确添加
    """
    try:
        # 读取处理后的文件
        df = pd.read_excel(file_path)
        
        print("验证排序字段结果:")
        print(f"总记录数: {len(df)}")
        print(f"排序字段非空记录数: {df['排序'].notna().sum()}")
        
        # 检查几个典型的父节点下的排序情况
        test_parents = [10000.0, 20000.0, 30000.0, 40000.0]
        
        for parent in test_parents:
            children = df[df['父节点编号'] == parent].sort_values('编号')
            if len(children) > 0:
                parent_name = children.iloc[0]['父节点名称']
                print(f"\n父节点 {parent} ({parent_name}) 的子节点排序:")
                for _, row in children.iterrows():
                    print(f"  编号: {row['编号']}, 名称: {row['名称']}, 排序: {row['排序']}")
        
        # 检查顶级节点的排序
        top_level = df[df['父节点编号'].isna()].sort_values('编号')
        print(f"\n顶级节点排序 (共{len(top_level)}个):")
        for _, row in top_level.iterrows():
            print(f"  编号: {row['编号']}, 名称: {row['名称']}, 排序: {row['排序']}")
        
        # 检查是否有排序字段为空的记录
        empty_sort = df[df['排序'].isna()]
        if len(empty_sort) > 0:
            print(f"\n警告: 发现 {len(empty_sort)} 条记录的排序字段为空:")
            print(empty_sort[['编号', '名称', '父节点编号', '父节点名称', '排序']])
        else:
            print("\n✓ 所有记录的排序字段都已正确填充")
            
        return True
        
    except Exception as e:
        print(f"验证时出错: {e}")
        return False

if __name__ == "__main__":
    verify_sorting_result("分类_带排序.xlsx")
