categoryId=newObj.getLong("documentCategoryID_DMS");
size=newObj.getLong("fileSize_baosteeluat");
/**这里查询视图表判断是否大小超限了**/
sql=getReadSqlExpression("CategoryDiskStatus_baosteeluat",user);
sql.comparsionAnd("id",ComparsionOperationEnum.EQUAL,categoryId);

sql.limit(0L,1L);
result=getObjects("CategoryDiskStatus_baosteeluat",sql,user);
if(!result.isSuccessful()){
    return result;
}

temp=result.getContent();
if(temp == null){
    return true;
}
if(temp.getLong("fileDivideSize_baosteeluat")<temp.getLong("used")){
    return getError("文件存储超限："+temp.getLong("used"));
}

return true;