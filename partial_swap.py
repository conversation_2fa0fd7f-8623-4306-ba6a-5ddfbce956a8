import pandas as pd

def swap_odd_even_positions(code):
    """
    将编码的奇数位置和偶数位置互换
    例如：100000 -> 010000
         103010 -> 013001
    """
    # 处理空值或NaN
    if pd.isna(code) or not code:
        return ""
    
    # 转换为字符串并确保是6位
    code_str = str(code).strip()
    if len(code_str) != 6:
        return code_str
    
    # 将字符串转换为列表便于操作
    chars = list(code_str)
    
    # 交换奇数位置和偶数位置（0-based索引）
    # 位置0和1交换，位置2和3交换，位置4和5交换
    for i in range(0, len(chars), 2):
        if i + 1 < len(chars):
            chars[i], chars[i + 1] = chars[i + 1], chars[i]
    
    return ''.join(chars)

def partial_swap_codes(df, swap_before_row=266):
    """
    只对指定行数之前的编号进行位置互换
    """
    # 创建编码映射字典（只包含需要转换的行）
    code_mapping = {}
    
    # 对前266行进行转换
    for index, row in df.head(swap_before_row).iterrows():
        original_code = row['编号']
        new_code = swap_odd_even_positions(original_code)
        code_mapping[original_code] = new_code
    
    # 对于266行之后的行，保持原编号不变
    for index, row in df.iloc[swap_before_row:].iterrows():
        original_code = row['编号']
        code_mapping[original_code] = original_code
    
    # 创建新的DataFrame
    result_df = df.copy()
    
    # 更新编号（只对前266行）
    for index in range(min(swap_before_row, len(df))):
        original_code = df.iloc[index]['编号']
        result_df.iloc[index, result_df.columns.get_loc('编号')] = code_mapping.get(original_code, original_code)
    
    # 更新父节点编号（所有行都需要检查，因为可能引用前266行的编号）
    for index in range(len(df)):
        original_parent = df.iloc[index]['父节点编号']
        if not pd.isna(original_parent) and original_parent:
            new_parent = code_mapping.get(str(original_parent), str(original_parent))
            result_df.iloc[index, result_df.columns.get_loc('父节点编号')] = new_parent if new_parent else ""
    
    return result_df, code_mapping

def main():
    """
    主函数
    """
    input_file = "编码转换结果.csv"
    swap_before_row = 266
    
    try:
        # 读取CSV文件，确保编号列作为字符串读取
        df = pd.read_csv(input_file, encoding='utf-8-sig', dtype={'编号': str, '父节点编号': str})
        
        print(f"原始数据总行数: {len(df)}")
        print(f"将对前{swap_before_row}行进行编号位置互换")
        print("\n原始数据示例（前10行）：")
        print("=" * 80)
        print(f"{'行号':<5} {'原编号':<10} {'名称':<30} {'原父节点编号':<12}")
        print("-" * 80)
        for index, row in df.head(10).iterrows():
            parent_code = row['父节点编号'] if not pd.isna(row['父节点编号']) else ""
            print(f"{index+1:<5} {row['编号']:<10} {row['名称']:<30} {parent_code:<12}")
        
        # 执行部分位置交换
        result_df, code_mapping = partial_swap_codes(df, swap_before_row)
        
        print(f"\n转换后数据示例（前10行）：")
        print("=" * 80)
        print(f"{'行号':<5} {'新编号':<10} {'名称':<30} {'新父节点编号':<12}")
        print("-" * 80)
        for index, row in result_df.head(10).iterrows():
            parent_code = row['父节点编号'] if not pd.isna(row['父节点编号']) else ""
            print(f"{index+1:<5} {row['编号']:<10} {row['名称']:<30} {parent_code:<12}")
        
        # 显示第266行前后的对比
        print(f"\n第{swap_before_row}行前后的对比：")
        print("=" * 80)
        print(f"{'行号':<5} {'编号':<10} {'名称':<30} {'状态':<10}")
        print("-" * 80)
        
        # 显示第264-268行
        start_row = max(0, swap_before_row - 3)
        end_row = min(len(df), swap_before_row + 3)
        
        for index in range(start_row, end_row):
            if index < len(result_df):
                row = result_df.iloc[index]
                status = "已转换" if index < swap_before_row else "未转换"
                print(f"{index+1:<5} {row['编号']:<10} {row['名称']:<30} {status:<10}")
        
        # 保存结果
        output_file = f"编码转换结果_前{swap_before_row}行互换.csv"
        result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"\n结果已保存到: {output_file}")
        print(f"共处理 {len(result_df)} 条记录")
        print(f"其中前{swap_before_row}行编号已进行位置互换")
        
        # 显示转换统计
        converted_count = min(swap_before_row, len(df))
        unchanged_count = max(0, len(df) - swap_before_row)
        
        print(f"\n转换统计：")
        print(f"已转换行数: {converted_count}")
        print(f"未转换行数: {unchanged_count}")
        print(f"总行数: {len(df)}")
        
        # 显示转换示例
        print(f"\n转换示例（前{swap_before_row}行）：")
        print("-" * 50)
        examples = []
        for index in range(min(5, swap_before_row, len(df))):
            original = df.iloc[index]['编号']
            converted = result_df.iloc[index]['编号']
            examples.append((original, converted))
        
        for original, converted in examples:
            print(f"{original} -> {converted}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
