attachments = getListJSONObjects(newObj, "files");
if ((attachments == null || attachments.isEmpty()) && newObj.getLong("nodeType") != null && newObj.getLong("nodeType").equals(0L)) {
    
    /** 当是文件夹类型且操作为新增或修改文件夹空间额度时，判断文件源剩余额度是否足够 **/
    FormLogic.getJsonFieldsValues(newObj, Arrays.asList("fileSource_baosteeluat", "folderQuato_baosteeluat"), "FileNode", user);
    
    check = false;
    /** 处理新增操作 **/
    if ("add".equals(event)) {
        check = true;
    } 
    /** 处理修改操作（检查folderQuato_baosteeluat字段是否变化且增大） **/
    else if ("update".equals(event)) {
        FormLogic.getJsonFieldsValues(oldObj, Arrays.asList("folderQuato_baosteeluat"), "FileNode", user);
    
        Long oldFolderQuato = oldObj.getLong("folderQuato_baosteeluat");
        Long newFolderQuato = newObj.getLong("folderQuato_baosteeluat");
        /** return getError("旧分配空间：" + oldFolderQuato + "，新分配空间: " + newFolderQuato); **/
    
        if (newFolderQuato != null && oldFolderQuato != null && newFolderQuato > oldFolderQuato) {
            check = true;
        }
    }

    if (check) {
        /** 获取新文件夹的文件源ID和分配额度 **/
        Long fileSourceId = newObj.getLong("fileSource_baosteeluat");
        Long folderQuato = newObj.getLong("folderQuato_baosteeluat");
        
        if (!NumberParser.isNullOrZero(fileSourceId) && !NumberParser.isNullOrZero(folderQuato)) {
            /** 查询文件源的总额度和已分配额度 **/
            sql = getReadSqlExpression("FileSourceSize_baosteeluat", user);
            sql.comparsionAnd("id", ComparsionOperationEnum.EQUAL, fileSourceId);
            
            /** 使用getSingleObject获取单个文件源对象 **/
            ServiceResult<JSONObject> sourceResult = getSingleObject("FileSourceSize_baosteeluat", sql, user);
            if (!sourceResult.isSuccessful() || sourceResult.getContent() == null) {
                return getError("未查询到文件源信息，文件源ID：" + fileSourceId);
            }
            
            JSONObject item = sourceResult.getContent();
            /** 检查关键字段是否为null **/
            Long totalQuota = item.getLong("total_quota_size");
            Long allocatedQuota = item.getLong("quato");
            if (totalQuota == null || allocatedQuota == null) {
                return getError("文件源配额信息不完整，总额度：" + totalQuota + "，已分配额度：" + allocatedQuota);
            }
            
            Long remainingQuota;
            /** 计算剩余额度（新增操作直接使用剩余额度，修改操作需要考虑新旧额度差值） **/
            if ("add".equals(event)) {
                remainingQuota = totalQuota - allocatedQuota;
            } else { 
                /** 修改操作：确保oldFolderQuato非空 **/
                Long oldFolderQuato = oldObj.getLong("folderQuato_baosteeluat");
                if (oldFolderQuato == null) {
                    return getError("未获取到旧文件夹额度信息");
                }
                /** 新增：确保计算过程中不出现null **/
                remainingQuota = totalQuota - (allocatedQuota - oldFolderQuato + folderQuato);
            }
            
            /** 检查剩余额度是否有效及足够 **/
            if (remainingQuota == null || folderQuato == null || folderQuato > remainingQuota) {
                return getError("文件源空间不足：" + "文件源名称:" + item.getString("title") + 
                               "，剩余可分配空间: " + remainingQuota + "，申请空间: " + folderQuato);
            }
        }
    }
}

return true;