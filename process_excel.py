import pandas as pd
import numpy as np

def add_sort_field_to_excel(file_path):
    """
    为Excel文件添加排序字段，相同父级节点的分类从10、20、30递增
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)

        print("原始数据结构:")
        print(df.head(10))
        print("\n列名:", df.columns.tolist())
        print("\n数据形状:", df.shape)

        # 为排序字段添加值
        df_processed = add_sort_values(df)

        # 保存处理后的文件
        output_file = file_path.replace('.xlsx', '_带排序.xlsx')
        df_processed.to_excel(output_file, index=False)
        print(f"\n处理完成！文件已保存为: {output_file}")

        # 显示处理后的部分数据
        print("\n处理后的数据示例:")
        print(df_processed[['编号', '名称', '父节点编号', '父节点名称', '排序']].head(20))

        return df_processed

    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def add_sort_values(df):
    """
    为相同父级节点的分类添加排序字段，从10、20、30递增
    """
    # 创建副本以避免修改原数据
    df_copy = df.copy()

    # 初始化排序字段
    df_copy['排序'] = np.nan

    # 获取所有唯一的父节点编号（包括NaN）
    parent_nodes = df_copy['父节点编号'].unique()

    print(f"\n开始处理排序字段...")
    print(f"发现 {len(parent_nodes)} 个不同的父节点")

    # 为每个父节点下的子节点分配排序号
    for parent_node in parent_nodes:
        if pd.isna(parent_node):
            # 处理顶级节点（父节点编号为NaN的节点）
            mask = df_copy['父节点编号'].isna()
        else:
            # 处理有父节点的子节点
            mask = df_copy['父节点编号'] == parent_node

        # 获取当前父节点下的所有子节点
        children = df_copy[mask].copy()

        if len(children) > 0:
            # 按编号排序以确保一致性
            children = children.sort_values('编号')

            # 为这些子节点分配排序号：10, 20, 30, 40, ...
            sort_values = [(i + 1) * 10 for i in range(len(children))]

            # 更新原数据框中的排序字段
            df_copy.loc[mask, '排序'] = sort_values

            if not pd.isna(parent_node):
                parent_name = children.iloc[0]['父节点名称'] if len(children) > 0 else "未知"
                print(f"父节点 {parent_node} ({parent_name}) 下有 {len(children)} 个子节点，排序号: {sort_values}")
            else:
                print(f"顶级节点共 {len(children)} 个，排序号: {sort_values}")

    return df_copy

if __name__ == "__main__":
    # 处理分类.xlsx文件
    df = add_sort_field_to_excel("分类.xlsx")
