<template>
  <el-tree show-checkbox style="max-width: 600px" :props="Props" :load="loadNode" lazy />
</template>

<script setup>
import { request } from 'utils'
const Props = {
  label: 'name_DMS',
  children: 'children',
  isLeaf: (data, node) => {
    console.log('节点数据:', data, 'isLeaf值:', data.isLeaf, '类型:', typeof data.isLeaf)
    return data.isLeaf
  }
}

const getData = async (parentId = 0) => {
  const params = {
    codename: 'DocumentCategory_DMS',
    pagecode: '0',
    pageTypeId: 1,
    needPageDefinition: false,
    needTotalCount: false,
    orderBy: [],
    skip: 0,
    take: 1000,
    parentId,
  }

  const { content } = await request({
    url: '/engine/form/generalForm/v1/getObjects',
    method: 'POST',
    data: params
  })
  return content.data
}

const loadNode = async (node, resolve, reject) => {
  if (node.level === 0) {
    const resp = await getData()
    console.log('根节点数据:', resp)
    return resolve(resp)
  }
  if (node.level >= 1) {

    const resp = await getData(node.data.id)
    console.log('子节点数据:', resp)
    return resolve(resp)
  }
}
</script>
