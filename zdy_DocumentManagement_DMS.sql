/*
 Navicat Premium Dump SQL

 Source Server         : 主数据系统
 Source Server Type    : PostgreSQL
 Source Server Version : 140011 (140011)
 Source Host           : *************:5432
 Source Catalog        : elsbiz
 Source Schema         : sl_baosteeluat

 Target Server Type    : PostgreSQL
 Target Server Version : 140011 (140011)
 File Encoding         : 65001

 Date: 29/07/2025 18:27:19
*/


-- ----------------------------
-- Table structure for zdy_DocumentManagement_DMS
-- ----------------------------
DROP TABLE IF EXISTS "sl_baosteeluat"."zdy_DocumentManagement_DMS";
CREATE TABLE "sl_baosteeluat"."zdy_DocumentManagement_DMS" (
  "id" int8 NOT NULL DEFAULT 0,
  "pagecode" int8 NOT NULL DEFAULT 0,
  "creatorId" int8 NOT NULL DEFAULT 0,
  "createTime" timestamp(6) NOT NULL DEFAULT timezone('utc'::text, now()),
  "lastModifyUserId" int8 NOT NULL DEFAULT 0,
  "lastModifyTime" timestamp(6) NOT NULL DEFAULT timezone('utc'::text, now()),
  "creatorRepresentative" text COLLATE "pg_catalog"."default",
  "lastModifyRepresentative" text COLLATE "pg_catalog"."default",
  "flowFlag" int4 NOT NULL DEFAULT 0,
  "workflowIds" int8[] NOT NULL DEFAULT ARRAY[]::bigint[],
  "masterWorkflowId" int8 NOT NULL DEFAULT 0,
  "masterWorkflowStepId" int8 NOT NULL DEFAULT 0,
  "masterWorkflowDefinitionId" int8 NOT NULL DEFAULT 0,
  "parentId" int8 NOT NULL DEFAULT 0,
  "parentPathIds" int8[] NOT NULL DEFAULT ARRAY[]::bigint[],
  "isLeaf" bool NOT NULL DEFAULT false,
  "code_DMS" text COLLATE "pg_catalog"."default",
  "documentName_DMS" text COLLATE "pg_catalog"."default",
  "documentCategoryID_DMS" int8 NOT NULL DEFAULT 0,
  "drafterID_DMS" int8 NOT NULL DEFAULT 0,
  "currentStatus_DMS" int4 NOT NULL DEFAULT 0,
  "version_DMS" text COLLATE "pg_catalog"."default",
  "attachments_DMS" jsonb[],
  "thumbnail_DMS" jsonb[],
  "paperFile_DMS" jsonb[],
  "priority_DMS" int4 NOT NULL DEFAULT 0,
  "storagePaperID_DMS" int8 NOT NULL DEFAULT 0,
  "collaborativeEditingIDs_DMS" int8[] DEFAULT '{}'::bigint[],
  "reviewersIDs_DMS" int8[] DEFAULT '{}'::bigint[],
  "approversIDs_DMS" int8[] DEFAULT '{}'::bigint[],
  "effectiveMethod_DMS" int4 NOT NULL DEFAULT 0,
  "reviewMethod_DMS" int4 NOT NULL DEFAULT 0,
  "reasonUpgradeID_DMS" int8 NOT NULL DEFAULT 0,
  "trainingFiles_DMS" jsonb[],
  "dataSources_DMS" int4 NOT NULL DEFAULT 0,
  "upgradeRemarks_DMS" text COLLATE "pg_catalog"."default",
  "creationMethod_DMS" int4 NOT NULL DEFAULT 0,
  "tsworkflowID_DMS" int8 NOT NULL DEFAULT 0,
  "workflowType_DMS" int4 NOT NULL DEFAULT 0,
  "fileType_DMS" int4 NOT NULL DEFAULT 0,
  "tsworkflowCode_DMS" text COLLATE "pg_catalog"."default",
  "SharingDepartmentIDs_DMS" int8[] DEFAULT '{}'::bigint[],
  "textReferenceIDs_DMS" int8[] DEFAULT '{}'::bigint[],
  "DF1_DMS" text COLLATE "pg_catalog"."default",
  "DocumentChangeID_DMS" int8 NOT NULL DEFAULT 0,
  "annex_DMS" jsonb[],
  "isAnnotations_DMS" bool NOT NULL DEFAULT false,
  "juID_DMS" text COLLATE "pg_catalog"."default",
  "trainingDate_DMS" date NOT NULL DEFAULT '1900-01-01'::date,
  "effectiveDate_DMS" date NOT NULL DEFAULT '1900-01-01'::date,
  "cycle_DMS" int4 NOT NULL DEFAULT 0,
  "isSecrecy_DMS" bool NOT NULL DEFAULT false,
  "isSpreadsheet_DMS" bool NOT NULL DEFAULT false,
  "ownDepartmentIDs_DMS" int8 NOT NULL DEFAULT 0,
  "ownDptIDs_DMS" int8[] DEFAULT '{}'::bigint[],
  "approvalStatus_DMS" int4 NOT NULL DEFAULT 0,
  "documentNameEn_DMS" text COLLATE "pg_catalog"."default",
  "revisionRecord_DMS" text COLLATE "pg_catalog"."default",
  "productionNumber_DMS" text COLLATE "pg_catalog"."default",
  "inspectionItems_DMS" text COLLATE "pg_catalog"."default",
  "isAnnex_DMS" bool NOT NULL DEFAULT false,
  "isLabel_DMS" bool NOT NULL DEFAULT false,
  "departmentUserID_DMS" int8 NOT NULL DEFAULT 0,
  "qadcUserID_DMS" int8 NOT NULL DEFAULT 0,
  "qaUserID_DMS" int8 NOT NULL DEFAULT 0,
  "approvalUserID_DMS" int8 NOT NULL DEFAULT 0,
  "distributionDptIDs_DMS" int8[] DEFAULT '{}'::bigint[],
  "codeNumberGeneratorId_DMS" int8 NOT NULL DEFAULT 0,
  "isCodeRecyled_DMS" bool NOT NULL DEFAULT false,
  "codeFromAbolishDocId_DMS" int8 NOT NULL DEFAULT 0,
  "repealReason_DMS" text COLLATE "pg_catalog"."default",
  "repealDate_DMS" date NOT NULL DEFAULT '1900-01-01'::date,
  "signaturePageID_DMS" int8 NOT NULL DEFAULT 0,
  "YSFL_lyg_baosteeluat" bool NOT NULL DEFAULT false,
  "wdjbfl_baosteeluat" text COLLATE "pg_catalog"."default",
  "WDJB_baosteeluat" text COLLATE "pg_catalog"."default",
  "jbfl_flag_baosteeluat" text COLLATE "pg_catalog"."default",
  "Datatype_DMS_baosteeluat" int4 NOT NULL DEFAULT 0,
  "knowledge_DMS_baosteeluat" int4 NOT NULL DEFAULT 1,
  "orderByNo_baosteeluat" numeric(35,15) NOT NULL DEFAULT 0,
  "wdjbfl2_baosteeluat" int8 NOT NULL DEFAULT 0,
  "versionChangeType_baosteeluat" int4 NOT NULL DEFAULT 0,
  "fileSize_baosteeluat" int8 NOT NULL DEFAULT 0,
  "distributionUseIDs_DMS_baosteeluat" int8[] DEFAULT '{}'::bigint[],
  "zd_wjlj_baosteeluat" text COLLATE "pg_catalog"."default",
  "documentNameTemp_baosteeluat" text COLLATE "pg_catalog"."default",
  "isPersonalDocuments_baosteeluat" bool DEFAULT false,
  "grbm_baosteeluat" text COLLATE "pg_catalog"."default",
  "grwdfl_baosteeluat" text COLLATE "pg_catalog"."default",
  "isZdyfl_baosteeluat" bool NOT NULL DEFAULT false,
  "grwdfl_2_baosteeluat" int8 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."id" IS 'id';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."pagecode" IS 'pagecode';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."creatorId" IS 'creatorId';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."createTime" IS 'createTime';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."lastModifyUserId" IS 'lastModifyUserId';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."lastModifyTime" IS 'lastModifyTime';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."creatorRepresentative" IS 'creatorRepresentative';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."lastModifyRepresentative" IS 'lastModifyRepresentative';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."flowFlag" IS 'flowFlag';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."workflowIds" IS 'workflowIds';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."masterWorkflowId" IS 'masterWorkflowId';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."masterWorkflowStepId" IS 'masterWorkflowStepId';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."masterWorkflowDefinitionId" IS 'masterWorkflowDefinitionId';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."parentId" IS 'parentId';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."parentPathIds" IS 'parentPathIds';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."isLeaf" IS 'isLeaf';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."code_DMS" IS '文档编号';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."documentName_DMS" IS '文档名称';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."documentCategoryID_DMS" IS '文档分类';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."drafterID_DMS" IS '起草人';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."currentStatus_DMS" IS '文档当前状态';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."version_DMS" IS '文档版本';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."attachments_DMS" IS '文档附件';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."thumbnail_DMS" IS '缩略图';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."paperFile_DMS" IS '纸质文件附件';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."priority_DMS" IS '文档优先级';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."storagePaperID_DMS" IS '纸质文档存放位置';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."collaborativeEditingIDs_DMS" IS '协同编辑人员';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."reviewersIDs_DMS" IS '文件审核人员';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."approversIDs_DMS" IS '文档批准人员';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."effectiveMethod_DMS" IS '生效方式';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."reviewMethod_DMS" IS '审核方式';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."reasonUpgradeID_DMS" IS '升版原因';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."trainingFiles_DMS" IS '培训相关文件';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."dataSources_DMS" IS '文档来源';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."upgradeRemarks_DMS" IS '升级备注';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."creationMethod_DMS" IS '创建方式';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."tsworkflowID_DMS" IS '定制流程';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."workflowType_DMS" IS '审批类型';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."fileType_DMS" IS '文件类型';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."tsworkflowCode_DMS" IS '指定流程';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."SharingDepartmentIDs_DMS" IS '分享部门';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."textReferenceIDs_DMS" IS '全文引用';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."DF1_DMS" IS '文档名';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."DocumentChangeID_DMS" IS '变更申请';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."annex_DMS" IS '纯净版附件';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."isAnnotations_DMS" IS '是否仅批注';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."juID_DMS" IS 'juID';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."trainingDate_DMS" IS '培训日期';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."effectiveDate_DMS" IS '生效日期';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."cycle_DMS" IS '周期';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."isSecrecy_DMS" IS '是否涉密';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."isSpreadsheet_DMS" IS '是否电子表格';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."ownDepartmentIDs_DMS" IS '所属部门';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."ownDptIDs_DMS" IS '所属部门';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."approvalStatus_DMS" IS '审批状态';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."documentNameEn_DMS" IS '文档英文名称';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."revisionRecord_DMS" IS '修订记录';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."productionNumber_DMS" IS '批生产记录号';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."inspectionItems_DMS" IS '检验项';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."isAnnex_DMS" IS '是否附件';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."isLabel_DMS" IS '是否为标签';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."departmentUserID_DMS" IS '部门负责人';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."qadcUserID_DMS" IS 'QADC';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."qaUserID_DMS" IS 'QA负责人';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."approvalUserID_DMS" IS '批准人';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."distributionDptIDs_DMS" IS '电子文档发放部门';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."codeNumberGeneratorId_DMS" IS 'code的号码生成器id';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."isCodeRecyled_DMS" IS '编号是否已回收';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."codeFromAbolishDocId_DMS" IS '编号来自废除单据的id';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."repealReason_DMS" IS '系统记录废止的原因';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."repealDate_DMS" IS '废止日期';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."signaturePageID_DMS" IS '签批页';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."YSFL_lyg_baosteeluat" IS '关联受控分类id';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."wdjbfl_baosteeluat" IS '文档级别分类';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."WDJB_baosteeluat" IS '文档级别分类';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."jbfl_flag_baosteeluat" IS '级别分类标识';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."Datatype_DMS_baosteeluat" IS '数据类型';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."knowledge_DMS_baosteeluat" IS '知识库分类';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."orderByNo_baosteeluat" IS '排序字段';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."wdjbfl2_baosteeluat" IS '文档级别分类2';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."versionChangeType_baosteeluat" IS '版本变更类型';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."fileSize_baosteeluat" IS '大小';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."distributionUseIDs_DMS_baosteeluat" IS '电子文档发放用户组';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."zd_wjlj_baosteeluat" IS '文件路径';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."documentNameTemp_baosteeluat" IS '新增文档名称';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."isPersonalDocuments_baosteeluat" IS '是否个人文档';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."grbm_baosteeluat" IS '个人文档编码';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."grwdfl_baosteeluat" IS '个人文档分类';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."isZdyfl_baosteeluat" IS '是否自定义分类';
COMMENT ON COLUMN "sl_baosteeluat"."zdy_DocumentManagement_DMS"."grwdfl_2_baosteeluat" IS '文档分类（个人文档）';
COMMENT ON TABLE "sl_baosteeluat"."zdy_DocumentManagement_DMS" IS '文档管理-';

-- ----------------------------
-- Indexes structure for table zdy_DocumentManagement_DMS
-- ----------------------------
CREATE INDEX "DocumentManagement_DMSParentIdIdx" ON "sl_baosteeluat"."zdy_DocumentManagement_DMS" USING btree (
  "parentId" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "DocumentManagement_DMSParentPathIdsGinIdx" ON "sl_baosteeluat"."zdy_DocumentManagement_DMS" USING gin (
  "parentPathIds" "pg_catalog"."array_ops"
);
CREATE INDEX "zdy_DocumentManagement_DMS_parentId_idx" ON "sl_baosteeluat"."zdy_DocumentManagement_DMS" USING btree (
  "parentId" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "zdy_DocumentManagement_DMS_parentPathIds_idx" ON "sl_baosteeluat"."zdy_DocumentManagement_DMS" USING gin (
  "parentPathIds" "pg_catalog"."array_ops"
);

-- ----------------------------
-- Triggers structure for table zdy_DocumentManagement_DMS
-- ----------------------------
CREATE TRIGGER "zdy_DocumentManagement_DMS_is_leaf_delete_trigger" AFTER DELETE ON "sl_baosteeluat"."zdy_DocumentManagement_DMS"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_is_leaf_trigger_func"();
CREATE TRIGGER "zdy_DocumentManagement_DMS_is_leaf_update_trigger" BEFORE INSERT OR UPDATE ON "sl_baosteeluat"."zdy_DocumentManagement_DMS"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_is_leaf_trigger_func"();
CREATE TRIGGER "zdy_DocumentManagement_DMS_parent_delete_trigger" AFTER DELETE ON "sl_baosteeluat"."zdy_DocumentManagement_DMS"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_parent_path_trigger_func"();
CREATE TRIGGER "zdy_DocumentManagement_DMS_parent_update_trigger" BEFORE INSERT OR UPDATE ON "sl_baosteeluat"."zdy_DocumentManagement_DMS"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_parent_path_trigger_func"();

-- ----------------------------
-- Primary Key structure for table zdy_DocumentManagement_DMS
-- ----------------------------
ALTER TABLE "sl_baosteeluat"."zdy_DocumentManagement_DMS" ADD CONSTRAINT "zdy_DocumentManagement_DMS_pkey" PRIMARY KEY ("id");
