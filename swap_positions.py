import pandas as pd

def swap_odd_even_positions(code):
    """
    将编码的奇数位置和偶数位置互换
    例如：100000 -> 010000
         103010 -> 013001
    """
    # 处理空值或NaN
    if pd.isna(code) or not code:
        return ""

    # 转换为字符串并确保是6位
    code_str = str(code).strip()
    if len(code_str) != 6:
        return code_str

    # 将字符串转换为列表便于操作
    chars = list(code_str)

    # 交换奇数位置和偶数位置（0-based索引）
    # 位置0和1交换，位置2和3交换，位置4和5交换
    for i in range(0, len(chars), 2):
        if i + 1 < len(chars):
            chars[i], chars[i + 1] = chars[i + 1], chars[i]

    return ''.join(chars)

def update_parent_codes(df):
    """
    更新父节点编号，因为编码格式改变了
    """
    # 创建编码映射字典
    code_mapping = {}
    for index, row in df.iterrows():
        original_code = row['编号']
        new_code = swap_odd_even_positions(original_code)
        code_mapping[original_code] = new_code
    
    # 更新编号
    df['新编号'] = df['编号'].apply(swap_odd_even_positions)
    
    # 更新父节点编号
    df['新父节点编号'] = df['父节点编号'].apply(lambda x: code_mapping.get(x, '') if x else '')
    
    return df

def main():
    """
    主函数
    """
    input_file = "编码转换结果.csv"
    
    try:
        # 读取CSV文件，确保编号列作为字符串读取
        df = pd.read_csv(input_file, encoding='utf-8-sig', dtype={'编号': str, '父节点编号': str})

        print("原始数据示例：")
        print("=" * 80)
        print(f"{'原编号':<10} {'名称':<30} {'原父节点编号':<12}")
        print("-" * 80)
        for index, row in df.head(10).iterrows():
            parent_code = row['父节点编号'] if not pd.isna(row['父节点编号']) else ""
            print(f"{row['编号']:<10} {row['名称']:<30} {parent_code:<12}")
        
        # 执行位置交换
        df_updated = update_parent_codes(df)
        
        print(f"\n转换后数据示例：")
        print("=" * 80)
        print(f"{'新编号':<10} {'名称':<30} {'新父节点编号':<12}")
        print("-" * 80)
        for index, row in df_updated.head(10).iterrows():
            print(f"{row['新编号']:<10} {row['名称']:<30} {row['新父节点编号']:<12}")
        
        # 创建最终结果DataFrame
        result_df = pd.DataFrame({
            '编号': df_updated['新编号'],
            '名称': df_updated['名称'],
            '父节点编号': df_updated['新父节点编号']
        })
        
        # 保存结果
        output_file = "编码转换结果_位置互换.csv"
        result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"\n完整转换结果：")
        print("=" * 80)
        print(f"{'编号':<10} {'名称':<30} {'父节点编号':<10}")
        print("-" * 80)
        
        for index, row in result_df.iterrows():
            print(f"{row['编号']:<10} {row['名称']:<30} {row['父节点编号']:<10}")
        
        print(f"\n结果已保存到: {output_file}")
        print(f"共处理 {len(result_df)} 条记录")
        
        # 显示转换示例
        print(f"\n转换示例：")
        print("-" * 50)
        examples = [
            ("100000", "010000"),
            ("101000", "011000"), 
            ("103010", "013001"),
            ("201000", "021000"),
            ("103020", "013002")
        ]
        
        for original, expected in examples:
            actual = swap_odd_even_positions(original)
            print(f"{original} -> {actual} {'✓' if actual == expected else '✗'}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
